let scrollProgress = 0;
const cards = document.querySelectorAll('.card');
const progressBar = document.getElementById('progressBar');
const title = document.getElementById('title');
const subtitle = document.getElementById('subtitle');
const scrollIndicator = document.getElementById('scrollIndicator');
const exploreButton = document.getElementById('exploreButton');
const categorySection = document.getElementById('categorySection');
const cardsContainer = document.getElementById('cardsContainer');
const hoverImage = document.getElementById('hoverImage');
const hoverEmoji = document.getElementById('hoverEmoji');

let currentExpandedCard = null;

// Cards data for the category section
const cardsData = [
    {
        icon: "🎨",
        title: "Web Design",
        subtitle: "Beautiful & Modern Designs",
        content: "Create stunning, responsive websites with modern design principles. We focus on user experience, visual hierarchy, and brand consistency to deliver designs that captivate and convert your audience.",
        hoverEmoji: "🎨"
    },
    {
        icon: "💻",
        title: "Web Development",
        subtitle: "Robust & Scalable Solutions",
        content: "Build powerful web applications using cutting-edge technologies. From frontend frameworks to backend APIs, we create scalable solutions that grow with your business needs.",
        hoverEmoji: "⚡"
    },
    {
        icon: "📱",
        title: "Mobile Development",
        subtitle: "Cross-Platform Apps",
        content: "Develop native and cross-platform mobile applications that provide seamless user experiences across iOS and Android devices. Optimized for performance and user engagement.",
        hoverEmoji: "📲"
    },
    {
        icon: "🚀",
        title: "Performance Optimization",
        subtitle: "Lightning Fast Websites",
        content: "Optimize your website's speed and performance with advanced techniques. Improve loading times, SEO rankings, and user satisfaction through comprehensive performance audits.",
        hoverEmoji: "⚡"
    },
    {
        icon: "🔒",
        title: "Security & Maintenance",
        subtitle: "Secure & Updated",
        content: "Keep your website secure and up-to-date with regular maintenance, security patches, and monitoring. Protect your business and users with enterprise-grade security measures.",
        hoverEmoji: "🛡️"
    },
    {
        icon: "📊",
        title: "Analytics & SEO",
        subtitle: "Data-Driven Growth",
        content: "Implement comprehensive analytics and SEO strategies to boost your online visibility. Track performance, understand user behavior, and optimize for search engines.",
        hoverEmoji: "📈"
    }
];

// Initial positions for each letter
const initialPositions = [
    { x: -800, y: -400, rotation: -15 }, // W
    { x: 800, y: -300, rotation: 20 },   // e
    { x: -700, y: 200, rotation: 10 },   // D
    { x: 900, y: 300, rotation: -25 },   // e
    { x: 0, y: -600, rotation: 5 }       // s
];

// Final positions (center with proper spacing to spell "WeDes")
const finalPositions = [
    { x: -240, y: 0, rotation: 0 }, // W
    { x: -120, y: 0, rotation: 0 }, // e
    { x: 0, y: 0, rotation: 0 },    // D
    { x: 120, y: 0, rotation: 0 },  // e
    { x: 240, y: 0, rotation: 0 }   // s
];

function updateAnimation() {
    const scrollTop = window.pageYOffset;
    const maxScroll = document.body.scrollHeight - window.innerHeight;
    scrollProgress = Math.min(scrollTop / maxScroll, 1);

    // Update progress bar
    progressBar.style.width = `${scrollProgress * 100}%`;

    // Phase 1: Text animation (0-50% scroll)
    const textPhaseProgress = Math.min(scrollProgress * 2, 1);
    
    // Show/hide title and scroll indicator
    if (textPhaseProgress > 0.2) {
        title.style.opacity = '1';
        subtitle.style.opacity = '1';
        scrollIndicator.style.opacity = '0';
    } else {
        title.style.opacity = '0';
        subtitle.style.opacity = '0';
        scrollIndicator.style.opacity = '1';
    }

    // Show explore button when text animation is nearly complete
    if (textPhaseProgress > 0.8) {
        exploreButton.style.opacity = '1';
    } else {
        exploreButton.style.opacity = '0';
    }

    // Update card positions for text animation
    cards.forEach((card, index) => {
        const initial = initialPositions[index];
        const final = finalPositions[index];
        
        // Use easing function for smooth animation
        const easeProgress = easeInOutCubic(textPhaseProgress);
        
        const currentX = initial.x + (final.x - initial.x) * easeProgress;
        const currentY = initial.y + (final.y - initial.y) * easeProgress;
        const currentRotation = initial.rotation + (final.rotation - initial.rotation) * easeProgress;
        const currentScale = 0.8 + (1 - 0.8) * easeProgress;
        
        card.style.transform = `translate(${currentX}px, ${currentY}px) rotate(${currentRotation}deg) scale(${currentScale})`;
        card.style.opacity = 0.3 + (0.7 * easeProgress);
    });

    // Phase 2: Category section (50-100% scroll)
    if (scrollProgress > 0.5) {
        categorySection.classList.add('visible');
    } else {
        categorySection.classList.remove('visible');
    }
}

function easeInOutCubic(t) {
    return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
}

// Throttled scroll event
let ticking = false;
function onScroll() {
    if (!ticking) {
        requestAnimationFrame(() => {
            updateAnimation();
            ticking = false;
        });
        ticking = true;
    }
}

window.addEventListener('scroll', onScroll);

// Initialize animation
updateAnimation();

// Smooth scrolling for better experience
document.documentElement.style.scrollBehavior = 'smooth';

// Explore button click handler - scroll to category section
exploreButton.addEventListener('click', () => {
    const targetScroll = document.body.scrollHeight * 0.6; // Scroll to 60% to show category section
    window.scrollTo({
        top: targetScroll,
        behavior: 'smooth'
    });
});

// Generate category cards
const supportsHover = window.matchMedia('(hover: hover)').matches;

cardsData.forEach((cardData, index) => {
    const card = document.createElement('div');
    card.className = 'category-card';
    card.innerHTML = `
        <div class="card-header">
            <div class="card-icon">${cardData.icon}</div>
            <div>
                <div class="card-title">${cardData.title}</div>
            </div>
        </div>
        <div class="card-subtitle">${cardData.subtitle}</div>
        <div class="card-content">${cardData.content}</div>
        <div class="expand-hint">💫</div>
    `;

    // Only add hover effects on devices that support hover (desktop)
    if (supportsHover && window.innerWidth > 768) {
        // Mouse enter event
        card.addEventListener('mouseenter', (e) => {
            // Don't show hover image if card is expanded
            if (!card.classList.contains('expanded')) {
                hoverEmoji.textContent = cardData.hoverEmoji;
                hoverImage.classList.add('visible');
            }
        });

        // Mouse move event
        card.addEventListener('mousemove', (e) => {
            // Only move image if card is not expanded
            if (!card.classList.contains('expanded')) {
                hoverImage.style.left = (e.clientX + 20) + 'px';
                hoverImage.style.top = e.clientY + 'px';
            }
        });

        // Mouse leave event
        card.addEventListener('mouseleave', () => {
            hoverImage.classList.remove('visible');
        });
    }

    // Click event for expansion
    card.addEventListener('click', (e) => {
        e.stopPropagation();
        
        if (currentExpandedCard && currentExpandedCard !== card) {
            currentExpandedCard.classList.remove('expanded');
        }

        if (card.classList.contains('expanded')) {
            card.classList.remove('expanded');
            currentExpandedCard = null;
        } else {
            card.classList.add('expanded');
            currentExpandedCard = card;
            
            // Hide hover image when card expands
            hoverImage.classList.remove('visible');
            
            // Smooth scroll to show the expanded card
            card.scrollIntoView({
                behavior: 'smooth',
                block: 'nearest',
                inline: 'center'
            });
        }
    });

    cardsContainer.appendChild(card);
});

// Click outside to collapse
document.addEventListener('click', (e) => {
    if (!e.target.closest('.category-card') && currentExpandedCard) {
        currentExpandedCard.classList.remove('expanded');
        currentExpandedCard = null;
    }
});

// Smooth horizontal scrolling with mouse wheel for category cards
cardsContainer.addEventListener('wheel', (e) => {
    if (e.deltaY !== 0) {
        e.preventDefault();
        cardsContainer.scrollLeft += e.deltaY;
    }
});
