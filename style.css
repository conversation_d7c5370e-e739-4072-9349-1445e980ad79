* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    overflow-x: hidden;
}

.container {
    height: 100vh; /* Height for text animation section only */
    position: relative;
}

.fixed-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: radial-gradient(circle at center, rgba(255,255,255,0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    pointer-events: none;
    z-index: 1;
}

.scroll-content {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.cards-container {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.card {
    position: absolute;
    width: 120px;
    height: 120px;
    background: linear-gradient(145deg, #667eea, #764ba2);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.3);
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
}

.letter {
    color: #ffffff;
    font-size: 72px;
    font-weight: 700;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    text-shadow: 0 4px 8px rgba(0,0,0,0.3);
    user-select: none;
}

/* Initial positions - cards start from edges */
.card:nth-child(1) {
    transform: translate(-800px, -400px) rotate(-15deg) scale(0.8);
}
.card:nth-child(2) {
    transform: translate(800px, -300px) rotate(20deg) scale(0.8);
}
.card:nth-child(3) {
    transform: translate(-700px, 200px) rotate(10deg) scale(0.8);
}
.card:nth-child(4) {
    transform: translate(900px, 300px) rotate(-25deg) scale(0.8);
}
.card:nth-child(5) {
    transform: translate(0px, -600px) rotate(5deg) scale(0.8);
}
.card:nth-child(6) {
    transform: translate(-600px, 0px) rotate(-10deg) scale(0.8);
}

.progress-bar {
    position: fixed;
    top: 0;
    left: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    z-index: 100;
    transition: width 0.1s ease;
}

.scroll-indicator {
    position: fixed;
    bottom: 50px;
    left: 50%;
    transform: translateX(-50%);
    color: rgba(255,255,255,0.6);
    font-size: 14px;
    z-index: 50;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateX(-50%) translateY(0);
    }
    40% {
        transform: translateX(-50%) translateY(-10px);
    }
    60% {
        transform: translateX(-50%) translateY(-5px);
    }
}

.title {
    position: fixed;
    top: 50px;
    left: 50%;
    transform: translateX(-50%);
    color: #ffffff;
    font-size: 48px;
    font-weight: 700;
    text-align: center;
    z-index: 50;
    opacity: 0;
    transition: opacity 0.6s ease;
}

.explore-button {
    position: fixed;
    bottom: 100px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(145deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 50px;
    font-size: 18px;
    font-weight: 600;
    cursor: pointer;
    z-index: 50;
    opacity: 0;
    transition: all 0.6s ease;
    text-decoration: none;
    display: inline-block;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.explore-button:hover {
    transform: translateX(-50%) translateY(-5px);
    box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
}

.subtitle {
    position: fixed;
    top: 120px;
    left: 50%;
    transform: translateX(-50%);
    color: rgba(255,255,255,0.8);
    font-size: 20px;
    font-weight: 400;
    text-align: center;
    z-index: 50;
    opacity: 0;
    transition: opacity 0.6s ease;
}

/* Category Section Styles */
.category-section {
    position: relative;
    width: 100%;
    min-height: 100vh;
    z-index: 5;
    display: flex;
    flex-direction: column;
    padding: 2rem 0;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.8s ease;
}

.category-section.visible {
    opacity: 1;
    transform: translateY(0);
}

.category-title {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 2rem;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-weight: bold;
    color: white;
}

.cards-container-category {
    display: flex;
    gap: 2rem;
    padding: 0 2rem;
    overflow-x: auto;
    overflow-y: visible;
    height: 400px;
    align-items: center;
    scrollbar-width: thin;
    scrollbar-color: #4ecdc4 transparent;
}

.cards-container-category::-webkit-scrollbar {
    height: 8px;
}

.cards-container-category::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
}

.cards-container-category::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    border-radius: 10px;
}

.category-card {
    min-width: 280px;
    height: 180px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.category-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(78, 205, 196, 0.3);
}

.category-card.expanded {
    min-width: 400px;
    height: 300px;
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(78, 205, 196, 0.5);
    z-index: 10;
}

.card-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.card-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.card-title {
    font-size: 1.2rem;
    font-weight: bold;
    color: white;
}

.card-subtitle {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 1rem;
}

.card-content {
    font-size: 0.9rem;
    line-height: 1.5;
    color: rgba(255, 255, 255, 0.8);
    opacity: 0;
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
}

.category-card.expanded .card-content {
    opacity: 1;
    max-height: 200px;
}

.expand-hint {
    position: absolute;
    bottom: 10px;
    right: 15px;
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.5);
    transition: all 0.3s ease;
}

.category-card.expanded .expand-hint::after {
    content: " (click to collapse)";
}

.category-card:not(.expanded) .expand-hint::after {
    content: " (click to expand)";
}

.hover-image {
    position: fixed;
    width: 200px;
    height: 150px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    pointer-events: none;
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    transform: translateY(-50%);
    overflow: hidden;
}

.hover-image.visible {
    opacity: 1;
}

.hover-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.hover-image.visible::before {
    left: 100%;
}

/* Mobile and tablet responsiveness */
@media (max-width: 1024px) {
    .category-title {
        font-size: 2rem;
        margin-bottom: 1.5rem;
        padding: 0 1rem;
    }

    .cards-container-category {
        padding: 0 1rem;
        height: auto;
        min-height: 350px;
    }

    .category-card {
        min-width: 260px;
        height: 160px;
        padding: 1.2rem;
    }

    .category-card.expanded {
        min-width: 320px;
        height: 280px;
    }
}

@media (max-width: 768px) {
    .card {
        width: 80px;
        height: 80px;
    }

    .letter {
        font-size: 48px;
    }

    .title {
        font-size: 32px;
        top: 30px;
    }

    .hover-image {
        display: none !important;
    }

    .category-title {
        font-size: 1.8rem;
        margin-bottom: 1rem;
    }

    .cards-container-category {
        gap: 1.5rem;
        padding: 0 1rem;
        height: auto;
        min-height: 300px;
    }

    .category-card {
        min-width: 240px;
        height: 140px;
        padding: 1rem;
    }

    .category-card.expanded {
        min-width: 280px;
        height: 260px;
    }

    .card-title {
        font-size: 1.1rem;
    }

    .card-subtitle {
        font-size: 0.85rem;
    }

    .card-content {
        font-size: 0.85rem;
    }

    .card-icon {
        width: 35px;
        height: 35px;
        font-size: 1.1rem;
    }
}

@media (max-width: 480px) {
    .category-section {
        padding: 0.5rem 0;
    }

    .category-title {
        font-size: 1.6rem;
        margin-bottom: 1rem;
    }

    .cards-container-category {
        gap: 1rem;
        padding: 0 0.5rem;
        min-height: 280px;
    }

    .category-card {
        min-width: 220px;
        height: 130px;
        padding: 0.8rem;
    }

    .category-card.expanded {
        min-width: 260px;
        height: 240px;
    }

    .card-title {
        font-size: 1rem;
    }

    .card-subtitle {
        font-size: 0.8rem;
        margin-bottom: 0.8rem;
    }

    .card-content {
        font-size: 0.8rem;
        line-height: 1.4;
    }

    .card-icon {
        width: 30px;
        height: 30px;
        font-size: 1rem;
    }

    .expand-hint {
        bottom: 8px;
        right: 12px;
        font-size: 0.7rem;
    }
}